#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 qwen_api.py 的功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ai_debugger.api.qwen_api import get_debug_instruction, debugger_analyze


def test_get_debug_instruction():
    """测试 get_debug_instruction 函数"""
    print("=" * 50)
    print("测试 get_debug_instruction 函数")
    print("=" * 50)
    
    # 测试用的调试信息
    test_step_output = """
    正在执行 JavaScript 函数: encrypt()
    当前变量值: password = "test123", salt = "abc"
    函数调用栈: main() -> login() -> encrypt()
    断点位置: line 45 in encrypt.js
    """
    
    try:
        print("输入的调试信息:")
        print(test_step_output)
        print("\n正在调用 API...")
        
        instruction = get_debug_instruction(test_step_output)
        
        print(f"✅ API 调用成功!")
        print(f"返回的调试指令: {instruction}")
        
        # 验证返回值是否有效
        valid_instructions = ["step_into", "step_over", "step_out"]
        if instruction in valid_instructions:
            print(f"✅ 返回指令有效: {instruction}")
            return True
        else:
            print(f"❌ 返回指令无效: {instruction}")
            return False
            
    except Exception as e:
        print(f"❌ API 调用失败: {e}")
        return False


def test_debugger_analyze():
    """测试 debugger_analyze 函数"""
    print("\n" + "=" * 50)
    print("测试 debugger_analyze 函数")
    print("=" * 50)
    
    # 创建测试用的日志文件
    test_log_content = """
JavaScript 调试日志 - 2025-01-27 10:30:00

=== 函数调用记录 ===
1. main() 开始执行
2. 调用 getUserInput() 获取用户输入
3. 调用 validateInput(input) 验证输入
4. 调用 encryptPassword(password) 加密密码
   - 使用 MD5 算法
   - 添加随机盐值: "xyz123"
   - 结果: "d41d8cd98f00b204e9800998ecf8427e"
5. 调用 sendToServer(encryptedData) 发送数据

=== 变量状态 ===
- userInput: "admin"
- password: "password123"
- encryptedPassword: "d41d8cd98f00b204e9800998ecf8427e"
- salt: "xyz123"

=== 网络请求 ===
POST /api/login
Headers: {"Content-Type": "application/json"}
Body: {"username": "admin", "password": "d41d8cd98f00b204e9800998ecf8427e"}
Response: {"status": "success", "token": "abc123def456"}

=== 错误记录 ===
无错误
"""
    
    # 确保result/logs目录存在
    log_dir = Path("result/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 确保result/report目录存在
    report_dir = Path("result/report")
    report_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建临时测试文件
    test_log_path = log_dir / "test_debug_log.txt"
    
    try:
        # 写入测试日志内容
        with open(test_log_path, 'w', encoding='utf-8') as f:
            f.write(test_log_content)
        
        print("创建测试日志文件:")
        print(f"文件路径: {test_log_path}")
        print(f"文件大小: {len(test_log_content)} 字符")
        
        print("\n正在调用 API 分析日志...")
        
        # 调用分析函数
        report_path = debugger_analyze(test_log_path)
        
        print(f"✅ API 调用成功!")
        print(f"生成的报告路径: {report_path}")
        
        # 检查报告文件是否创建成功
        if Path(report_path).exists():
            print("✅ 报告文件创建成功")
            
            # 读取并显示报告内容的前几行
            with open(r'E:\AI浏览器的JS逆向\result\logs\debug_data-2025-07-27-18-48-59.txt', 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            print("\n报告内容预览:")
            print("-" * 30)
            lines = report_content.split('\n')[:10]  # 只显示前10行
            for line in lines:
                print(line)
            if len(report_content.split('\n')) > 10:
                print("... (省略更多内容)")
            
            return True
        else:
            print(f"❌ 报告文件未创建: {report_path}")
            return False
            
    except Exception as e:
        print(f"❌ API 调用失败: {e}")
        return False
    finally:
        # 清理测试文件
        if test_log_path.exists():
            test_log_path.unlink()
            print(f"\n清理测试文件: {test_log_path}")


def main():
    """主测试函数"""
    print("开始测试 qwen_api.py 功能...")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试结果
    results = []
    
    # 测试1: get_debug_instruction
    result1 = test_get_debug_instruction()
    results.append(("get_debug_instruction", result1))
    
    # 测试2: debugger_analyze
    result2 = test_debugger_analyze()
    results.append(("debugger_analyze", result2))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过! qwen_api.py 工作正常。")
    else:
        print("\n⚠️  部分测试失败，请检查API配置和网络连接。")
    
    return all_passed


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc() 