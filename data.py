import requests

cookies = {
    'xlly_s': '1',
    'plugin_home_downLoad_cookie': '%E4%B8%8B%E8%BD%BD%E6%8F%92%E4%BB%B6',
    '__cn_logon__': 'false',
    'trackId': 'a0adf57e5ab145f196a3dfdc9ba65361',
    'cna': 'Y53WIO5P5RgCAXt3GvgfRAuq',
    'union': '{"amug_biz":"oneself","amug_fl_src":"awakeId_984","creative_url":"https%3A%2F%2Fair.1688.com%2Fkapp%2FinnovateHub%2Fextension-offer-search%2FimageSearch%3Famug_biz%3Doneself%26amug_fl_src%3DawakeId_984%26language%3Dzh-CN","creative_time":1753669733012}',
    'leftMenuLastMode': 'COLLAPSE',
    'leftMenuModeTip': 'shown',
    'mtop_partitioned_detect': '1',
    '_m_h5_tk': 'd0a2b7f38d4dfbdb915caad80b224759_1753690276702',
    '_m_h5_tk_enc': '4a28e0b11d1c2be35658688cbe4914f2',
    'x5sec': '7b22733b32223a2231323832343534306332633833633239222c22617365727665723b33223a22307c43496d306e4d5147454c724d756573484d4b724779716e382f2f2f2f2f77453d227d',
    '_user_vitals_session_data_': '{"user_line_track":true,"ul_session_id":"izxzvdyqaah","last_page_id":"air.1688.com%2Ffrv0odgnzkk"}',
    'tfstk': 'gkMZWkDIOdpNeXUTsvwVzPpuNg2TI-8Woxabmmm0fP4M6fNmToZ5oZmDWqk4-VKTl-wM3juEJstTlhIdnqzK1ZaXXjzm52FbsRtAgto48OG66SaV0y0a1hHqDtrmmqKTlhdIXceYnUTS3LitX7lB_vLQnnm3ho5hR64xXceAvM_DNXmO0LVsN-q0oHy30uIgnoXMYwq0-OqcIobHYyU3jlXgjpV30uWcIq2DYD4Lm-qio5xExveboz6U45x32wrXCemg_zmg8ToKLlqO6c4FhtMET5zlFyWcnvrZ0ET8ZTRbrbU_NWuHdOyqxouYwxTRBPVrpjPoFEWKx7U_CA2yMa4o92o8ivYl3knExjNqdUJUOvmtOWlpJLgzQqPoIWSDDDoEtJl43IQurX3E2SDHrtrKTVHUdAxX6RGSVxFoId78R7Eqk7HXyOUgigolkk2BJxhNosVgvkzWYH-jbOhKCKjvxsCY6XEUPhKOMsFgvkzWYH5AM5eLYzt9X',
    'isg': 'BMjIrPt8NGykI1ggxAzlJ4OVmTbacSx7b0PTn4J5MMM2XWjHKoMqC6Jb0TUt7eRT',
}

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6',
    'cache-control': 'no-cache',
    'origin': 'https://air.1688.com',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://air.1688.com/kapp/innovateHub/extension-offer-search/imageSearch/offer-viewer-market',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
    'x-1688extension-secret': 'tMrw7DZ7MZzkt0fGiWrinjC0W8MI3THcnZv47XYOY3ib2aIZvUKUEKlBp6ruMMBfIGe/aJ5D2vuRaX+ukJW+UaxsmDuLJcbqklV8mfWZCjON6Ifpl4qu/m3iQ73x49kG8oWlnmhrYWXhCnSjLtrBFZ+JynVlSyHnz528D+VZIlL5MpfW5U+Sm6pUt/15ZMQ3QAe8P+J4uq72aRYj5QrepnvWnW3CTQv3wJM3DwZSDVC4BY7eD/DNw0GesEPu76KQ6xWplTUJluxYz7D+g3VZT5cgpvCE',
    'x-accept-language': 'zh-CN',
    'cookie': 'ali_apache_id=***********.17428872485.607570.9; cna=N9ivH8Y7mFkCAXj04M1Y2/fD; t=86ed3af00d3ec3b2280f4e3fbac34fff; leftMenuLastMode=COLLAPSE; leftMenuModeTip=shown; xlly_s=1; plugin_home_downLoad_cookie=%E4%B8%8B%E8%BD%BD%E6%8F%92%E4%BB%B6; trackId=b22cfbd9d9f146c5ae6cc46ed9c7ee6c; union={"amug_biz":"oneself","amug_fl_src":"awakeId_984","creative_url":"https%3A%2F%2Fair.1688.com%2Fkapp%2FinnovateHub%2Fextension-offer-search%2FimageSearch%3Famug_biz%3Doneself%26amug_fl_src%3DawakeId_984%26language%3Dzh-CN","creative_time":1753679799562}; mtop_partitioned_detect=1; _m_h5_tk=8009db4a52d05fff99a68febd3ffb226_1753698335196; _m_h5_tk_enc=02bef7b7bbb716dc15e063848bf1ac72; x5sec=7b22733b32223a2236653534383263363263393833383535222c22617365727665723b33223a22307c434a48586e4d5147454d763468704d434d4b724779716e382f2f2f2f2f77453d227d; _user_vitals_session_data_={"user_line_track":true,"ul_session_id":"da025yx9gd","last_page_id":"air.1688.com%2Fr9e1c96hmsj"}; tfstk=gmv-WutzYq0oSdvRm3l0xnXPb-immjxy0U-_KwbuOELv-UIhO_sQco_v8aflFUXpAH72q6xhEW6pbFLUZXW7pktwW62kZTApHETMtQfHVwwp-Hvk-TOkp9BynQ2hr4WpAFXKSVDiI3-PU9giSJ0L_WXCAvbIi_w_V0AsSVDinbZfp43M-KKWrmIOf9s5O8MjDGI5dz_WRZ1ffG45deTCGSsGb7Z5P__fGZSCdwTCdmBfuMBCRv3b2wq5JJdRMMQm_-S7dJ9A2_GDVZwC0KIRwNtWeJwQvgCR53_x7xuNnstF9prUpatW_eS6PzgV-CxDFsBKsy7pt39ke9rUMnCpEB5pUcaNWQtvpTY-WW76Zn6fzQg3Na9yjtdCM0MWDTQJ7LT-BqT16LAF9Lo_7NdWDpBkeDVP4ITPHiJnu8_pGB9la9zSuwAMmL1ACgS6IdH6rPbOx7nxDWPFNikekWS5h_-YMiQimSVUTsjVDNmxDWPFNiSASmD0TW5c0; isg=BFFRhfYe3efy_TEVhnZhLx6tYF3rvsUw0XkHrzPkS5hJ2nEsew2ZAcIufq48Ul1o',
}

params = {
    'jsv': '2.7.2',
    'appKey': '12574478',
    't': '1753684710190',
    'sign': '1cadd1e32ecd26e0cb135f1ef3aaaaf1',
    'dataType': 'json',
    'api': 'mtop.1688.pc.plugin.imageSearch.plugin.search',
    'v': '1.1',
    'type': 'originaljson',
    'data': '{"params":"{\\"searchScene\\":\\"imageEx\\",\\"serviceGroupName\\":\\"service.group.pc.image\\",\\"interfaceName\\":\\"imageExtraSearchService\\",\\"serviceParam.extendParam[subChannel]\\":\\"pc_image_plugin\\",\\"serviceParam.extendParam[imageId]\\":\\"1485508524552225841\\",\\"serviceParam.extendParam[appName]\\":\\"imageExtra\\",\\"abRequest.level3Biz\\":\\"main\\",\\"abRequest.level2Biz\\":\\"image\\",\\"abRequest.level1Biz\\":\\"search\\",\\"serviceParam.extendParam[pageSize]\\":40,\\"serviceParam.extendParam[beginPage]\\":1,\\"serviceParam.extendParam[tags]\\":\\"\\",\\"serviceParam.extendParam[province]\\":\\"\\",\\"serviceParam.extendParam[city]\\":\\"\\",\\"serviceParam.extendParam[quantityBegin]\\":0,\\"serviceParam.extendParam[sortField]\\":\\"\\"}","scene":"IMAGE_SEARCH_DRAWER"}',
}
n = 0
for _ in range(1,50):
    n+=1
    response = requests.get(
        'https://h5api.m.1688.com/h5/mtop.1688.pc.plugin.imagesearch.plugin.search/1.1/',
        params=params,
        # cookies=cookies,
        headers=headers,
    )

    if '["SUCCESS::调用成功"]' in response.text:
        print('成功',n)
        print(response.text)
    else:
        print('失败')
        print(response.text)
