# 更新日志

所有项目的显著变更都将记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [0.3.0] - 2025-04-04

### 新增
- 新增对百度文心ERNIE API的支持
- 新增对讯飞星火API的支持
- 新增对大型混淆JavaScript代码的自动分析能力

### 优化
- 重构API调用模块，提高代码可维护性
- 优化调试信息收集逻辑，减少无关信息干扰
- 改进分析报告生成算法，提供更精准的加密逻辑分析
- 优化内存占用，提高大型站点分析效率
  - 实现增量数据收集和处理，避免一次性加载大量数据
  - 添加内存监控和资源释放机制，自动管理内存使用
  - 优化断点处理逻辑，减少不必要的断点触发
  - 实现数据分片处理，对大型JavaScript文件进行分段分析
  - 使用LRU缓存策略管理脚本源代码，自动清理不常用脚本

## [0.2.0] - 2025-04-04

### 优化
- 优化AI提示词，提高JavaScript调试效率
  - 增强对JavaScript特有加密API的识别能力
  - 添加混淆代码识别和处理策略
  - 改进调试决策规则，更智能地处理eval和动态执行代码
  - 优化分析报告生成的提示词，提供更详细的加密逻辑分析
- 改进对大型混淆JavaScript代码的处理能力

## [0.1.0] - 2025-03-17

### 新增
- 初版发布

### 后续计划
- 支持更多浏览器（如Firefox、Edge等）
- 添加自动化测试框架
- 开发图形用户界面(GUI)
