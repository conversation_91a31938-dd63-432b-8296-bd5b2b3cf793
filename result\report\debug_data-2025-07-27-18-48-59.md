# JavaScript 加密分析报告

## 原始日志
result\logs\debug_data-2025-07-27-18-48-59.txt

从提供的调试信息中，我可以分析出以下关键加密相关内容：

### 1. 加解密方法识别
- 主要使用CryptoJS库，模式包括：
  - CFB (Cipher Feedback)
  - CTR (Counter)
  - OFB (Output Feedback)
  - CTR<PERSON>ladman (特殊CTR实现)
- 核心加密类是`PasswordBasedCipher`，采用对称加密
- 使用AES加密算法（从静态类`CryptoUtils`中的`AES_KEY`和`AES_IV_LENGTH`确认）

### 2. 密钥提取
- 静态AES密钥：`'mysecretkey12345'`（在`CryptoUtils`类中）
- IV长度：16字节（`AES_IV_LENGTH`）
- 密钥生成逻辑：通过KDF（密钥派生函数）从密码生成，调用了`kdf.execute()`
- 密钥存储在内存中，通过`PasswordBasedCipher`类的配置对象传递

### 3. 关键代码分析
核心加密逻辑简化：
```javascript
// 加密流程
function encrypt(password, data) {
  const salt = CryptoJS.lib.WordArray.random(8);
  const derived = kdf.execute(password, keySize, ivSize, salt);
  const cipherParams = CryptoJS.AES.encrypt(data, derived.key, { 
    iv: derived.iv,
    mode: CryptoJS.mode.CFB  // 或其他模式
  });
  return { ciphertext: cipherParams, salt: salt };
}
```

### 4. mitmproxy脚本示例
```python
from Crypto.Cipher import AES
from mitmproxy import http

AES_KEY = b'mysecretkey12345'  # 实际应从JS中提取动态密钥

def decrypt(data: bytes, iv: bytes) -> str:
    cipher = AES.new(AES_KEY, AES.MODE_CFB, iv=iv)
    return cipher.decrypt(data).decode()

def request(flow: http.HTTPFlow) -> None:
    if "encrypted=" in flow.request.url:
        encrypted = flow.request.query.get("encrypted")
        iv = flow.request.query.get("iv")
        if encrypted and iv:
            decrypted = decrypt(encrypted, iv)
            flow.request.query["decrypted"] = decrypted

def response(flow: http.HTTPFlow) -> None:
    if flow.response.headers.get("Content-Type") == "application/json":
        data = flow.response.json()
        if data.get("encrypted"):
            iv = bytes.fromhex(data["iv"])
            data["decrypted"] = decrypt(data["encrypted"], iv)
            flow.response.text = json.dumps(data)
```

注意：实际使用时需要根据以下调整：
1. 确认实际的加密模式（CFB/CTR/OFB）
2. 提取运行时生成的动态IV
3. 处理可能的Base64编码层

