import json
import os
from pathlib import Path
import requests

# Local prompts
from .prompt_templates import (
    DEBUG_INSTRUCTION_PROMPT,
    DEBUGGER_ANALYZE_PROMPT,
    SYSTEM_ROLE_PROMPT,
)

# =====================================
# Silicon Flow DeepSeek-V3 API settings
# =====================================

API_KEY = "sk-vzntvcouseuricvgaujuoxngvuiyapknftmariqiqdhxcnlf"
BASE_URL = "https://api.siliconflow.cn/v1/chat/completions"
MODEL_NAME = "deepseek-ai/DeepSeek-R1"

# Common headers for all requests
HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
}

# -------------------------------------
# Helper function for chat completion
# -------------------------------------


def _chat_completion(payload: dict) -> dict:
    """Call Silicon Flow chat completion endpoint and return JSON response."""

    # Ensure model and stream are always present
    payload.setdefault("model", MODEL_NAME)
    payload.setdefault("stream", False)

    response = requests.post(BASE_URL, headers=HEADERS, json=payload, timeout=120,proxies={"http":None,"https":None})
    response.raise_for_status()
    return response.json()


def get_debug_instruction(step_output: str) -> str:
    # 压缩调试信息，去除多余空格和换行
    compressed_info = " ".join(step_output.split())
    
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "system", "content": DEBUG_INSTRUCTION_PROMPT},
            {"role": "system", "content": f"当前调试信息：{compressed_info}"},
        ],
        "response_format": {"type": "json_object"},
        "stream": False,
    }

    completion = _chat_completion(payload)
 
    try:
        # Silicon Flow returns the same structure as OpenAI-style API
        result = completion["choices"][0]["message"]["content"]
        resp = json.loads(result)
        
        if resp.get("step_into", False):
            instruction = "step_into"
        elif resp.get("step_out", False):
            instruction = "step_out"
        else:
            instruction = "step_over"  
            
    except Exception as e:
        print("调用远程 API 失败，默认使用 step_over。错误信息：", e)
        instruction = "step_over"
    return instruction


def debugger_analyze(path):
    path = Path(path)
    
    # 读取文件内容
    with open(path, 'r', encoding='utf-8') as f:
        file_content = f.read()
    
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "system", "content": SYSTEM_ROLE_PROMPT},
            {"role": "user", "content": f"{DEBUGGER_ANALYZE_PROMPT}\n\n{file_content}"},
        ],
        "stream": False,
    }

    completion = _chat_completion(payload)

    if completion.get("choices") and completion["choices"][0].get("message"):
        ai_response = completion["choices"][0]["message"]["content"].strip()
    else:
        raise ValueError("未获取到有效响应内容")

    # 生成Markdown报告
    md_content = f"""# JavaScript 加密分析报告

## 原始日志
{path}

{ai_response}

"""

    output_dir = Path("result/report")
    output_dir.mkdir(parents=True, exist_ok=True)
    output_path = output_dir / f"{path.stem}.md"

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(md_content)

    return str(output_path)