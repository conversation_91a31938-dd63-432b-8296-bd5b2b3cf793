import json
import os
import sys
import requests
from pathlib import Path
from .base_api import BaseAPI
from .prompt_templates import DEBUG_INSTRUCTION_PROMPT, DEBUGGER_ANALYZE_PROMPT, SYSTEM_ROLE_PROMPT

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# SiliconFlow API 配置
API_KEY = "sk-vzntvcouseuricvgaujuoxngvuiyapknftmariqiqdhxcnlf"
BASE_URL = "https://api.siliconflow.cn/v1/chat/completions"
MODEL_NAME = "Qwen/Qwen3-235B-A22B-Instruct-2507"


def parse_stream_response(response):
    """解析流式响应数据"""
    full_content = ""
    
    # 确保响应以UTF-8编码处理
    response.encoding = 'utf-8'
    
    for line in response.iter_lines(decode_unicode=True):
        if not line:
            continue
            
        # 跳过注释行
        if line.startswith(':'):
            continue
            
        # 处理 data: 开头的行
        if line.startswith('data: '):
            data_str = line[6:]  # 去掉 'data: ' 前缀
            
            # 检查是否是结束标记
            if data_str.strip() == '[DONE]':
                break
                
            try:
                data = json.loads(data_str)
                
                # 提取内容
                if 'choices' in data and len(data['choices']) > 0:
                    delta = data['choices'][0].get('delta', {})
                    content = delta.get('content', '')
                    
                    if content:
                        full_content += content
                        
            except json.JSONDecodeError:
                continue
            except Exception as e:
                continue
    
    return full_content


def get_debug_instruction(step_output: str) -> str:
    # 压缩调试信息，去除多余空格和换行
    compressed_info = " ".join(step_output.split())
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {
                'role': 'system', 
                'content': DEBUG_INSTRUCTION_PROMPT
            },
            {
                'role': 'system',
                'content': f'当前调试信息：{compressed_info}'
            }
        ],
        "response_format": {"type": "json_object"},
        "stream": False,  # 调试指令不需要流式，保持快速响应
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        # 检查响应格式
        if not result.get('choices') or not result['choices'][0].get('message'):
            print("API 响应格式异常，使用默认指令")
            return "step_over"
            
        content = result['choices'][0]['message']['content']
        
        # 检查内容是否为空
        if not content or content.strip() == "":
            print("API 返回空内容，使用默认指令")
            return "step_over"
            
        resp = json.loads(content)
        
        if resp.get("step_into", False):
            instruction = "step_into"
        elif resp.get("step_out", False):
            instruction = "step_out"
        else:
            instruction = "step_over"  
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求失败，默认使用 step_over。错误信息：{e}")
        instruction = "step_over"
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败，默认使用 step_over。错误信息：{e}")
        instruction = "step_over"
    except Exception as e:
        print(f"调用远程 API 失败，默认使用 step_over。错误信息：{e}")
        instruction = "step_over"
    return instruction


def debugger_analyze(path):
    path = Path(path)
    
    # 读取文件内容，确保UTF-8编码
    try:
        with open(path, 'r', encoding='utf-8') as f:
            file_content = f.read()
    except UnicodeDecodeError:
        # 如果UTF-8失败，尝试其他编码
        with open(path, 'r', encoding='gbk') as f:
            file_content = f.read()
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json; charset=utf-8"
    }
    
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {
                'role': 'system', 
                'content': f'以下是需要分析的日志文件内容：\n{file_content}'
            },
            {
                'role': 'user', 
                'content': DEBUGGER_ANALYZE_PROMPT
            }
        ],
        "stream": True,  # 启用流式输出
        "temperature": 0.7,
        "max_tokens": 8000
    }
    
    try:
        response = requests.post(BASE_URL, headers=headers, json=payload, timeout=120, stream=True)
        response.raise_for_status()
        
        # 解析流式响应
        ai_response = parse_stream_response(response)
        
        if not ai_response or ai_response.strip() == "":
            raise ValueError("未获取到有效响应内容")
            
    except requests.exceptions.RequestException as e:
        raise ValueError(f"网络请求失败: {e}")
    except Exception as e:
        raise ValueError(f"API调用失败: {e}")

    # 生成Markdown报告，确保UTF-8编码
    md_content = f"""# JavaScript 加密分析报告

## 原始日志
{path}

{ai_response}

"""
    output_path = f"result/report/{path.stem}.md"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(md_content)

    return output_path
