#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有道翻译签名生成算法还原
基于调试日志的逆向分析结果
"""

import hashlib
import time
from typing import Dict, Any


class YoudaoSignGenerator:
    """有道翻译签名生成器"""
    
    def __init__(self):
        # 从调试日志中分析得到的常量（这些可能需要根据实际情况调整）
        self.client = "fanyideskweb"  # 常见的有道客户端标识
        self.product = "webfanyi"     # 产品标识
        self.app_version = "1.0.0"    # 应用版本
        self.vendor = "web"           # 厂商
        self.point_param = ""         # 点参数
        self.keyfrom = "fanyi.web"    # 来源标识
        self.mid = "1"                # 中间件ID
        self.screen = "1920x1080"     # 屏幕分辨率
        self.model = "webfanyi"       # 模型
        self.network = "wifi"         # 网络类型
        self.abtest = ""              # A/B测试参数
    
    def _hash_function(self, text: str) -> str:
        """
        哈希函数 - 对应JavaScript中的 _(text) 函数
        从日志中看到 .digest("hex")，很可能是MD5
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def S(self, mystic_time: int, key: str) -> str:
        """
        对应JavaScript中的 S(e, t) 函数
        构造签名字符串：client=${d}&mysticTime=${e}&product=${u}&key=${t}
        """
        sign_string = f"client={self.client}&mysticTime={mystic_time}&product={self.product}&key={key}"
        return self._hash_function(sign_string)
    
    def generate_sign_params(self, key: str, yduuid: str = None) -> Dict[str, Any]:
        """
        对应JavaScript中的 k(e, t) 函数
        生成完整的签名参数
        
        Args:
            key: 密钥（从日志中看到如 "yU5nT5dK3eZ1pI4j", "SRz6r3IGA6lj9i5zW0OYqgVZOtLDQe3e"）
            yduuid: 用户UUID（可选）
        
        Returns:
            包含签名和其他参数的字典
        """
        # 获取当前时间戳（毫秒）
        mystic_time = int(time.time() * 1000)
        
        # 生成签名
        sign = self.S(mystic_time, key)
        
        # 构造完整参数
        params = {
            "sign": sign,
            "client": self.client,
            "product": self.product,
            "appVersion": self.app_version,
            "vendor": self.vendor,
            "pointParam": self.point_param,
            "mysticTime": mystic_time,
            "keyfrom": self.keyfrom,
            "mid": self.mid,
            "screen": self.screen,
            "model": self.model,
            "network": self.network,
            "abtest": self.abtest,
            "yduuid": yduuid or "abcdefg"  # 默认值
        }
        
        return params
    
    def verify_with_log_data(self):
        """
        使用日志中的数据验证算法正确性
        """
        print("=== 验证签名算法 ===")
        
        # 从日志中提取的测试数据
        test_cases = [
            {
                "key": "yU5nT5dK3eZ1pI4j",
                "mystic_time": 1753668797906,
                "expected_sign_string": f"client={self.client}&mysticTime=1753668797906&product={self.product}&key=yU5nT5dK3eZ1pI4j"
            },
            {
                "key": "SRz6r3IGA6lj9i5zW0OYqgVZOtLDQe3e", 
                "mystic_time": 1753668918699,
                "expected_sign_string": f"client={self.client}&mysticTime=1753668918699&product={self.product}&key=SRz6r3IGA6lj9i5zW0OYqgVZOtLDQe3e"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i} ---")
            print(f"密钥: {test_case['key']}")
            print(f"时间戳: {test_case['mystic_time']}")
            print(f"签名字符串: {test_case['expected_sign_string']}")
            
            # 计算签名
            calculated_sign = self._hash_function(test_case['expected_sign_string'])
            print(f"计算出的签名: {calculated_sign}")


def main():
    """主函数 - 演示使用方法"""
    generator = YoudaoSignGenerator()
    
    # 验证算法
    generator.verify_with_log_data()
    
    print("\n" + "="*60)
    print("=== 实际使用示例 ===")
    
    # 使用日志中的密钥生成签名
    key1 = "yU5nT5dK3eZ1pI4j"
    key2 = "SRz6r3IGA6lj9i5zW0OYqgVZOtLDQe3e"
    
    print(f"\n--- 使用密钥1: {key1} ---")
    params1 = generator.generate_sign_params(key1)
    for k, v in params1.items():
        print(f"{k}: {v}")
    
    print(f"\n--- 使用密钥2: {key2} ---")
    params2 = generator.generate_sign_params(key2, "custom_uuid_123")
    for k, v in params2.items():
        print(f"{k}: {v}")
    
    print("\n" + "="*60)
    print("=== 签名生成步骤详解 ===")
    print("1. 获取当前时间戳（毫秒）")
    print("2. 构造签名字符串：client={client}&mysticTime={timestamp}&product={product}&key={key}")
    print("3. 对签名字符串进行MD5哈希，得到十六进制字符串")
    print("4. 将签名和其他参数组合成完整的请求参数")


if __name__ == "__main__":
    main() 