|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 12462 📝 代码上下文: /",h=c.appKey||("waptest"===d.subDomain?"4272":"12574478"),j=(new Date).getTime(),k=i(d.token+"&"+j+"&"+h+"&"+c.data),l={jsv:A,appKey:h,t:j,sign:k},m=➤{data:c.data,ua:c.ua};Object.keys(c).forEach(function(a){"undefined"==typeof l[a]&&"undefined"==typeof m[a]&&"headers"!==a&&"ext_headers"!==a&&"ext_qu 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:12462) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 12484 📝 代码上下文: st "===d.subDomain?" 4272 ":" 12574478 "),j=(new Date).getTime(),k=i(d.token+" & "+j+" & "+h+" & "+c.data),l={jsv:A,appKey:h,t:j,sign:k},m={data:c.dat➤a,ua:c.ua};Object.keys(c).forEach(function(a){" undefined "==typeof l[a]&&" undefined "==typeof m[a]&&" headers "!==a&&" ext_headers "!==a&&" ext_querys "!==a&&(l[a]=c[a]) 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:12484) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 12649 📝 代码上下文: forEach(function(a) { "undefined" == typeof l[a] && "undefined" == typeof m[a] && "headers" !== a && "ext_headers" !== a && "ext_querys" !== a➤ && (l[a] = c[a]) }), c.ext_querys && Object.keys(c.ext_querys).forEach(function(a) { l[a] = c.ext_querys[a] }), d.getJSONP ? l.type = "jsonp" : d.getOriginalJSONP ? l.type = "originaljsonp" : (d.getJSON | 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:12649) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 12722 📝 代码上下文: headers "!==a&&" ext_headers "!==a&&" ext_querys "!==a&&(l[a]=c[a])}),c.ext_querys&&Object.keys(c.ext_querys).forEach(function(a){l[a]=c.ext_querys[a]➤}),d.getJSONP?l.type=" jsonp ":d.getOriginalJSONP?l.type=" originaljsonp ":(d.getJSON||d.postJSON)&&(l.type=" originaljson ")," undefined "!=typeof c.valueType&&(" 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:12722) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 12868 📝 代码上下文: ), d.getJSONP ? l.type = "jsonp" : d.getOriginalJSONP ? l.type = "originaljsonp" : (d.getJSON || d.postJSON) && (l.type = "originaljson"), "undefined"➤ != typeof c.valueType && ("original" === c.valueType ? d.getJSONP || d.getOriginalJSONP ? l.type = "originaljsonp" : (d.getJSON || d.postJSON) && (l.type = "originaljson") : "string" === c.valueT 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:12868) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 13115 📝 代码上下文: ostJSON) && (l.type = "originaljson"): "string" === c.valueType && (d.getJSONP || d.getOriginalJSONP ? l.type = "jsonp" : (d.getJSON || d.postJSON) &&➤ (l.type = "json"))), d.useJsonpResultType === !0 && "originaljson" === l.type && delete l.type, d.dangerouslySetProtocol && (g = d.dangerouslySetProtocol + ":" + g), "5.0" === c.SV && (g += "5.0/", 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:13115) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 13181 📝 代码上下文: ONP || d.getOriginalJSONP ? l.type = "jsonp" : (d.getJSON || d.postJSON) && (l.type = "json"))), d.useJsonpResultType === !0 && "originaljson" === l.t➤ype && delete l.type, d.dangerouslySetProtocol && (g = d.dangerouslySetProtocol + ":" + g), "5.0" === c.SV && (g += "5.0/", f()), d.querystring = l, d.postdata = m, d.path = g } b() }, q.prototype.__proc 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:13181) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 13252 📝 代码上下文: = "json"))), d.useJsonpResultType === !0 && "originaljson" === l.type && delete l.type, d.dangerouslySetProtocol && (g = d.dangerouslySetProtocol + ":➤" + g), "5.0" === c.SV && (g += "5.0/", f()), d.querystring = l, d.postdata = m, d.path = g } b() }, q.prototype.__processUnitPrefix = function(a) { a() }; var E = 0; q.prototype.__requestJSONP = funct 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:13252) 2. <匿名函数> (脚本ID:255, 行:1, 列:18253) 🔍 作用域变量: 📋 局部作用域 (<匿名函数 0>): c: {"type": "post", "data": "{\"utdid\":\"ofuvzfay7rh\"}", "api": "mtop.1688.pc.pclog.queryTask", "v": "1.0", "ecode": 0, "dataType": "json", "timeout": 20000, "jsonpIncPrefix": "ns8lnx00rps"} d: {"_note": "[属性过多，显示 15/16]", "token": "dd0bb4493e0f551b9b02fd8d06ea0406", "H5Request": true, "WindVaneRequest": false, "LoginRequest": "[未知值]", "AntiCreep": "[未知值]", "_truncated": "[达到最大属性限制]"} _truncated: "[达到最大属性限制]" ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18347 📝 代码上下文: se { var g, h = c(), i = c(); e.push(function() { return h = c(), g = a.call(d, function(a) { return h.resolve(a),➤ i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](function(a) { h.reject(a) })), h.promise }), f.push(function(a) { return i.resolve(a), g }) } } var d = this, e = [], f = []; a.forEach(b); for (var g, h = s; g = e 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18347) 🔍 作用域变量: [作用域中未找到相关变量] ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18392 📝 代码上下文: h = c(), g = a.call(d, function(a) { return h.resolve(a), i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](funct➤ion(a) { h.reject(a) })), h.promise }), f.push(function(a) { return i.resolve(a), g }) } } var d = this, e = [], f = []; a.forEach(b); for (var g, h = s; g = e.shift();) h = h.then(g); for (; g = f.pop();) h = h.the 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18392) 🔍 作用域变量: [作用域中未找到相关变量] ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18399 📝 代码上下文: g = a.call(d, function(a) { return h.resolve(a), i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](function(a) { h.rej➤ect(a) })), h.promise }), f.push(function(a) { return i.resolve(a), g }) } } var d = this, e = [], f = []; a.forEach(b); for (var g, h = s; g = e.shift();) h = h.then(g); for (; g = f.pop();) h = h.then(g); re 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18399) 🔍 作用域变量: [作用域中未找到相关变量] ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18236 📝 代码上下文: e = v.ERROR, a.retJson = b }) }, q.prototype.__sequence = function(a) { function b(a) { if (a instanceof Array) a.forEach(b); ➤ else { var g, h = c(), i = c(); e.push(function() { return h = c(), g = a.call(d, function(a) { return h.resolve(a), i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](function(a) { h.reject(a) 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18236) 🔍 作用域变量: [作用域中未找到相关变量] ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18250 📝 代码上下文: tJson = b }) }, q.prototype.__sequence = function(a) { function b(a) { if (a instanceof Array) a.forEach(b); else { ➤ var g, h = c(), i = c(); e.push(function() { return h = c(), g = a.call(d, function(a) { return h.resolve(a), i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](function(a) { h.reject(a) })), h.promise } 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18250) 🔍 作用域变量: [作用域中未找到相关变量] ||
|| 🔍 调试器已暂停 || 📍 暂停位置: <匿名函数> 在 脚本ID: 255 📍 具体位置: 行 1, 列 18347 📝 代码上下文: se { var g, h = c(), i = c(); e.push(function() { return h = c(), g = a.call(d, function(a) { return h.resolve(a),➤ i.promise }, function(a) { return h.reject(a), i.promise }), g && (g = g["catch"](function(a) { h.reject(a) })), h.promise }), f.push(function(a) { return i.resolve(a), g }) } } var d = this, e = [], f = []; a.forEach(b); for (var g, h = s; g = e 🔄 调用堆栈: 1. <匿名函数> (脚本ID:255, 行:1, 列:18347) 🔍 作用域变量: [作用域中未找到相关变量] ||
