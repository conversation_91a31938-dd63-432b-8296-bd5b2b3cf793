{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "dgeafmgkobkeendinpademeoiaagknoj": {"account_extension_type": 0, "active_permissions": {"api": ["alarms", "contextMenus", "cookies", "storage", "webNavigation", "webRequest", "declarativeNetRequest", "declarativeNetRequestFeedback", "scripting"], "explicit_host": ["<all_urls>", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["*://s.1688.com/selloffer/offer_search.htm*", "*://s.1688.com/selloffer/similar_search.htm*", "*://s.1688.com/youyuan/index.htm*", "<all_urls>", "http://sogou.com/*", "https://*.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html*", "https://detail.1688.com/*", "https://sogou.com/*", "https://trade.1688.com/order/*", "https://www.baidu.com/*", "https://www.so.com/*", "https://www.sogou.com/*"]}, "commands": {"screenshot": {"suggested_key": "Ctrl+Shift+2", "was_assigned": true}, "show_entry": {"suggested_key": "Ctrl+Shift+1", "was_assigned": true}}, "content_settings": [], "creation_flags": 38, "disable_reasons": [], "dnr_dynamic_ruleset": {"checksum": **********}, "filtered_service_worker_events": {"webNavigation.onCompleted": [{}]}, "first_install_time": "13398145406234198", "from_webstore": false, "granted_permissions": {"api": ["alarms", "contextMenus", "cookies", "storage", "webNavigation", "webRequest", "declarativeNetRequest", "declarativeNetRequestFeedback", "scripting"], "explicit_host": ["<all_urls>", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["*://s.1688.com/selloffer/offer_search.htm*", "*://s.1688.com/selloffer/similar_search.htm*", "*://s.1688.com/youyuan/index.htm*", "<all_urls>", "http://sogou.com/*", "https://*.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html*", "https://detail.1688.com/*", "https://sogou.com/*", "https://trade.1688.com/order/*", "https://www.baidu.com/*", "https://www.so.com/*", "https://www.sogou.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398145406234198", "location": 4, "newAllowFileAccess": true, "path": "E:\\1688-extension", "persistent_script_url_patterns": ["https://*.1688.com/*"], "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0.11"}, "serviceworkerevents": ["alarms.onAlarm", "commands.onCommand", "contextMenus.onClicked", "runtime.onInstalled", "storage.onChanged", "tabs.onActivated", "tabs.onUpdated", "webRequest.onCompleted/s1"], "uninstall_url": "https://air.1688.com/kapp/assets-group/haobangshou/UninstallRetention?amug_biz=oneself&amug_fl_src=awakeId_984&extension=AL35bheNeGs2VsNN_1.0.11_production", "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}, "ui": {"developer_mode": true}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "F9A68CAABB282C1E01DFC610D26350A5EB685DA1A4BFD3DBEE87BBD70A233B10"}, "extensions": {"ui": {"developer_mode": "B6E71B07C6782BCB295B7AF105A3466907F609E0F006E75E0A14A32D8414C7AC"}}, "homepage": "BC7042FF9B57243A8D5D227385D72AFD75405F375B2028F096B82C40B368C444", "homepage_is_newtabpage": "99856B06BD4BFEECC669E41893F0EE87A423358CD0249518709E39C0FD0B7680", "session": {"restore_on_startup": "7D423F42EA8D04530D51B270B1539FE6EC5424CC6C9B32D8E30D3A5D6139E47F", "startup_urls": "1A000F2B9DE7B79B19185E41CD3EED7149370AC2A626EFFE4835171178C956E6"}}, "browser": {"show_home_button": "F64A2C604AAB21AFE93C76143C5119C99AC0FFD04F095A97AC14D1C104FD10FE"}, "default_search_provider_data": {"template_url_data": "95499EF74F4E4ABE4EC7766A25FA782322A4972C41498EFBD1753DFC5F6F3399"}, "enterprise_signin": {"policy_recovery_token": "DF5DAF96CDEABA5B82746ADA08404BF0593AAE48AABB8D526B1CA153EFC1BC98"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "2BA48B9FA95E546B2BF984E4568A6CFDAEE3ED57825ADFE5D98F01476BE0AEEE", "dgeafmgkobkeendinpademeoiaagknoj": "19A3887ABADC96E7909FEC15C24732BD69BFC162A1FA292E64BD6AC26B44444B", "fignfifoniblkonapihmkfakmlgkbkcf": "4895988B793E3D93DB4E9601630E1138439F47C35327E87102A29057A9B6F6FE", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "D5D84997D56D8B8EFC663C86832EDA019AE111844713D42551C97E9A5D6535B7", "nkeimhogjdpnpccoofpliimaahmaaome": "6DF2332F146AB45B6BB118C1E7F9E573BDDE1D43957EE125B5B6352B0B2AE7DC"}, "ui": {"developer_mode": "95DB3457C14D97612371C91CBC2CA12CCC503D78CA044E0806861EB64F56C2CA"}}, "google": {"services": {"account_id": "A8666C794632DBBE4A1FF373AA867DE2CF65643BAFA9626448071F6908533FD9", "last_signed_in_username": "70C7D788F9A601B26A560CEE95F7AFD20A2BAEBE47973FB9345F6097853B77D7", "last_username": "F971CC5254ED1F9C14850EAAED0CF0264B2896818F5A00EF8B671D7EBA91B531"}}, "homepage": "30A7E1E2A9962A81BA8096D103BB0A14F07FFEF8BE743CB0BB650429C8CEECEE", "homepage_is_newtabpage": "67ABB47123F37A334FBF5B5C04D6149F06FD1A7958BA46C6A20A497B6728E296", "media": {"cdm": {"origin_data": "EBC176B5AF9CB252A673FA19AFE41CF8722154171DB4AE0B5A132CC13AF14D87"}, "storage_id_salt": "F75E90AF7811800DB554973C35604E6017B6F0C95B5037B69B2C383A27BA98EB"}, "module_blocklist_cache_md5_digest": "42A73D5CE5D0126D08FA3676EA226753E752DDA2CA8E2894FA12E85AD92DA324", "pinned_tabs": "FF71487816AFE9AAD52FB0F833F14693AA350278B5C330F43598EBA1E19020A6", "prefs": {"preference_reset_time": "735F82DB11A74D96C85D812AE7AADEECB439D7B1578DA28EFA2AC6C69B4C4E1B"}, "safebrowsing": {"incidents_sent": "72B0F9A5FE315ECF6EF66E809F19E7AAB9324FD8E970FF62A5D0BE3D74DED0FA"}, "search_provider_overrides": "37D37568B4733AB74B0CA13BE0C924030E1F3A57C538EF105CEDB864D099040C", "session": {"restore_on_startup": "FD8C59CACA26B35A3BFBFEC26FEFAECD54E2DAB601CE2E1B9D673E66E5A2FCF1", "startup_urls": "F40F62976E68B705FFFAB99CB5D048EEB9B4C424EDE62B2D336E437089532A9C"}}, "super_mac": "79924857EBE426C66C7D2EC666D9191AB33422887EC036054849DDFECE30B748"}}