import hashlib
import time

def generate_sign(token, timestamp, app_key, data):
  """
  根据提供的逻辑生成 sign。

  Args:
    token: d.token 的值。
    timestamp: 时间戳 (毫秒)。
    app_key: 应用密钥 (h)。
    data: 请求的数据 (c.data)。

  Returns:
    生成的 sign 字符串 (MD5 hash)。
  """
  # 按照抓取到的顺序拼接字符串
  raw_string = f"{token}&{timestamp}&{app_key}&{data}"
  # print(f"Sign Input String: {raw_string}") # 可用于调试，查看输入

  # 使用 MD5 哈希
  sign = hashlib.md5(raw_string.encode('utf-8')).hexdigest()
  return sign

# --- 示例用法 ---
# 从你的调试信息中提取或替换这些值
token_from_debugger = "dd0bb4493e0f551b9b02fd8d06ea0406"
# 生成当前时间戳 (毫秒)
current_timestamp = int(time.time() * 1000)
# 从你的调试信息中提取 appKey 相关逻辑，这里假设是 "12574478"
app_key_from_debugger = "12574478"
# 从你的调试信息中提取 c.data
data_from_debugger = '{"params":"{\\"searchScene\\":\\"imageEx\\",\\"serviceGroupName\\":\\"service.group.pc.image\\",\\"interfaceName\\":\\"imageExtraSearchService\\",\\"serviceParam.extendParam[subChannel]\\":\\"pc_image_plugin\\",\\"serviceParam.extendParam[imageId]\\":\\"1485508524552225841\\",\\"serviceParam.extendParam[appName]\\":\\"imageExtra\\",\\"abRequest.level3Biz\\":\\"main\\",\\"abRequest.level2Biz\\":\\"image\\",\\"abRequest.level1Biz\\":\\"search\\",\\"serviceParam.extendParam[pageSize]\\":40,\\"serviceParam.extendParam[beginPage]\\":1,\\"serviceParam.extendParam[tags]\\":\\"\\",\\"serviceParam.extendParam[province]\\":\\"\\",\\"serviceParam.extendParam[city]\\":\\"\\",\\"serviceParam.extendParam[quantityBegin]\\":0,\\"serviceParam.extendParam[sortField]\\":\\"\\"}","scene":"IMAGE_SEARCH_DRAWER"}'

# 生成 sign
generated_sign = generate_sign(token_from_debugger, current_timestamp, app_key_from_debugger, data_from_debugger)

print(f"Generated Sign: {generated_sign}")
print(f"Used Timestamp: {current_timestamp}")
