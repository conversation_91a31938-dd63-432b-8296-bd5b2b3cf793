#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688扩展接口 - 获取 x-1688extension-secret
通过模拟浏览器环境与Chrome扩展交互 (Puppeteer版本)
"""

import json
import time
import uuid
import base64
import hashlib
import hmac
import os
import platform
import tempfile
import shutil
from typing import Dict, Any, Optional
from pathlib import Path
import asyncio
try:
    from pyppeteer import launch
    from pyppeteer.browser import Browser
    from pyppeteer.page import Page
except ImportError:
    print("请安装 pyppeteer: pip install pyppeteer")

def find_chrome_executable():
    """查找系统中的Chrome可执行文件"""
    system = platform.system()
    
    if system == "Windows":
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        ]
    elif system == "Darwin":  # macOS
        possible_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        ]
    else:  # Linux
        possible_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/snap/bin/chromium",
        ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def create_temp_profile_dir():
    """创建临时用户配置目录"""
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="chrome_profile_")
        print(f"创建临时Chrome配置目录: {temp_dir}")
        return temp_dir
    except Exception as e:
        print(f"创建临时目录失败: {e}")
        # 使用当前目录下的临时文件夹
        temp_dir = os.path.join(os.getcwd(), f"chrome_temp_{int(time.time())}")
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir

class Extension1688Interface:
    """1688扩展接口类 (Puppeteer版本)"""
    
    def __init__(self, extension_path: str = None):
        """
        初始化扩展接口
        
        Args:
            extension_path: Chrome扩展的路径
        """
        self.extension_path = extension_path
        self.profile_dir = None
        self.browser = None
        self.page = None
        self.extension_id = None
        
    async def setup_chrome_with_extension(self) -> Browser:
        """设置带有扩展的Chrome浏览器"""
        try:
            # 查找系统Chrome
            chrome_path = find_chrome_executable()
            if not chrome_path:
                print("未找到系统Chrome，将使用pyppeteer下载的Chromium")
                chrome_path = None
            else:
                print(f"找到Chrome: {chrome_path}")
            
            # 创建临时配置目录
            self.profile_dir = create_temp_profile_dir()
            
            launch_options = {
                'headless': False,  # 显示浏览器窗口
                'args': [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-background-networking',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-ipc-flooding-protection',
                    '--disable-extensions-file-access-check',
                    '--disable-extensions-http-throttling',
                    '--disable-component-extensions-with-background-pages',
                    '--enable-extensions',  # 启用扩展
                    '--allow-file-access-from-files',  # 允许文件访问
                    '--disable-extensions-except',  # 禁用其他扩展（留空表示不禁用任何）
                    '--test-type',  # 测试模式
                    '--ignore-certificate-errors',  # 忽略证书错误
                    '--allow-running-insecure-content',  # 允许不安全内容
                    '--disable-popup-blocking',  # 禁用弹窗阻止
                    f'--user-data-dir={self.profile_dir}',  # 使用临时用户数据目录
                ],
                'ignoreDefaultArgs': [
                    '--enable-automation',
                    '--disable-extensions',
                    '--disable-default-apps',
                    '--disable-component-extensions-with-background-pages'
                ],
                'dumpio': False,  # 关闭浏览器日志避免干扰
                'devtools': False,  # 不自动打开开发者工具
            }
            
            # 如果有扩展路径，添加扩展参数
            if self.extension_path and os.path.exists(self.extension_path):
                # 移除之前的 --disable-extensions-except 空参数
                launch_options['args'] = [arg for arg in launch_options['args'] if not arg.startswith('--disable-extensions-except')]
                # 添加扩展相关参数
                launch_options['args'].extend([
                    f'--load-extension={self.extension_path}',
                    f'--disable-extensions-except={self.extension_path}',
                    '--enable-extension-activity-logging',  # 启用扩展活动日志
                ])
                print(f"将加载扩展: {self.extension_path}")
            else:
                print("未指定扩展路径，可以手动加载扩展")
            
            # 如果找到系统Chrome，使用它
            if chrome_path:
                launch_options['executablePath'] = chrome_path
            
            print("正在启动浏览器...")
            self.browser = await launch(**launch_options)
            print("Chrome浏览器启动成功")
            
            # 创建新页面
            self.page = await self.browser.newPage()
            
            # 设置用户代理
            await self.page.setUserAgent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 设置视口
            await self.page.setViewport({'width': 1280, 'height': 720})
            
            return self.browser
            
        except Exception as e:
            print(f"启动Chrome失败: {e}")
            # 清理临时目录
            if self.profile_dir and os.path.exists(self.profile_dir):
                try:
                    shutil.rmtree(self.profile_dir)
                except:
                    pass
            raise
    
    async def load_extension_manually(self) -> bool:
        """手动加载扩展（如果扩展路径存在）"""
        try:
            if not self.extension_path or not os.path.exists(self.extension_path):
                print(f"扩展路径不存在: {self.extension_path}")
                return False
            
            # 访问扩展管理页面
            await self.page.goto("chrome://extensions/", {'waitUntil': 'networkidle2'})
            await asyncio.sleep(2)
            
            # 启用开发者模式
            await self.page.evaluate("""
                () => {
                    const toggle = document.querySelector('extensions-manager')
                        ?.shadowRoot?.querySelector('extensions-toolbar')
                        ?.shadowRoot?.querySelector('#devMode');
                    if (toggle && !toggle.checked) {
                        toggle.click();
                    }
                }
            """)
            await asyncio.sleep(1)
            
            print(f"请手动在Chrome扩展页面加载扩展目录: {self.extension_path}")
            print("1. 点击 '加载已解压的扩展程序'")
            print("2. 选择扩展文件夹")
            print("3. 完成后按回车继续...")
            input()
            
            return True
            
        except Exception as e:
            print(f"加载扩展失败: {e}")
            return False
    
    async def get_extension_id(self) -> Optional[str]:
        """获取扩展ID"""
        try:
            # 访问扩展管理页面
            await self.page.goto("chrome://extensions/")
            await asyncio.sleep(3)
            
            # 执行JavaScript获取扩展信息
            script = """
            () => {
                return new Promise((resolve) => {
                    try {
                        // 尝试从DOM获取扩展ID
                        const extensions = document.querySelectorAll('extensions-item');
                        console.log('找到扩展数量:', extensions.length);
                        
                        for (let ext of extensions) {
                            try {
                                const shadowRoot = ext.shadowRoot;
                                if (shadowRoot) {
                                    const name = shadowRoot.querySelector('#name')?.textContent || '';
                                    const id = ext.getAttribute('id');
                                    console.log('找到扩展:', name, id);
                                    if (name.includes('1688') || name.includes('阿里') || name.includes('alibaba')) {
                                        resolve(id);
                                        return;
                                    }
                                }
                            } catch (e) {
                                console.log('处理扩展项时出错:', e);
                            }
                        }
                        
                        // 如果没找到特定扩展，返回第一个扩展的ID
                        if (extensions.length > 0) {
                            const firstId = extensions[0].getAttribute('id');
                            console.log('使用第一个扩展ID:', firstId);
                            resolve(firstId);
                        } else {
                            console.log('未找到任何扩展');
                            resolve(null);
                        }
                    } catch (e) {
                        console.log('获取扩展ID时出错:', e);
                        resolve(null);
                    }
                });
            }
            """
            
            extension_id = await self.page.evaluate(script)
            if extension_id:
                self.extension_id = extension_id
                print(f"找到扩展ID: {extension_id}")
                return extension_id
            else:
                print("未找到扩展ID")
                return None
                
        except Exception as e:
            print(f"获取扩展ID失败: {e}")
            return None
    
    async def navigate_to_1688(self) -> bool:
        """导航到1688网站"""
        try:
            print("正在导航到1688网站...")
            await self.page.goto("https://www.1688.com", {'waitUntil': 'networkidle2', 'timeout': 30000})
            await asyncio.sleep(3)
            print("已导航到1688网站")
            return True
        except Exception as e:
            print(f"导航到1688失败: {e}")
            return False
    
    async def inject_monitor_script(self) -> bool:
        """注入监控脚本来捕获网络请求"""
        try:
            script = """
            () => {
                // 创建一个全局变量来存储捕获的headers
                window.captured1688Secret = null;
                window.captured1688Headers = [];
                
                console.log('开始注入网络监控脚本...');
                
                // 重写XMLHttpRequest
                const originalXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    const xhr = new originalXHR();
                    const originalSetRequestHeader = xhr.setRequestHeader;
                    
                    xhr.setRequestHeader = function(name, value) {
                        if (name === 'x-1688extension-secret') {
                            window.captured1688Secret = value;
                            console.log('捕获到 x-1688extension-secret:', value);
                        }
                        window.captured1688Headers.push({name: name, value: value});
                        return originalSetRequestHeader.call(this, name, value);
                    };
                    
                    return xhr;
                };
                
                // 重写fetch
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    if (options && options.headers) {
                        for (const [key, value] of Object.entries(options.headers)) {
                            if (key === 'x-1688extension-secret') {
                                window.captured1688Secret = value;
                                console.log('捕获到 x-1688extension-secret (fetch):', value);
                            }
                            window.captured1688Headers.push({name: key, value: value});
                        }
                    }
                    return originalFetch.apply(this, arguments);
                };
                
                console.log('网络监控脚本已注入完成');
            }
            """
            
            await self.page.evaluate(script)
            print("网络监控脚本注入成功")
            return True
            
        except Exception as e:
            print(f"注入监控脚本失败: {e}")
            return False
    
    async def trigger_extension_activity(self) -> bool:
        """触发扩展活动以生成secret"""
        try:
            # 尝试触发一些可能会调用扩展API的操作
            urls_to_visit = [
                "https://air.1688.com/kapp/innovateHub/extension-offer-search/imageSearch",
                "https://s.1688.com/selloffer/offer_search.htm?keywords=手机",
                "https://detail.1688.com/offer/679304936488.html"
            ]
            
            for url in urls_to_visit:
                try:
                    print(f"正在访问: {url}")
                    await self.page.goto(url, {'waitUntil': 'networkidle2', 'timeout': 30000})
                    await asyncio.sleep(5)
                    
                    # 滚动页面
                    await self.page.evaluate('window.scrollTo(0, document.body.scrollHeight);')
                    await asyncio.sleep(2)
                    
                    # 尝试点击一些元素
                    await self._try_click_elements()
                    
                    # 检查是否捕获到secret
                    secret = await self.get_captured_secret()
                    if secret:
                        return True
                        
                except Exception as e:
                    print(f"访问 {url} 时出错: {e}")
                    continue
            
            return False
            
        except Exception as e:
            print(f"触发扩展活动失败: {e}")
            return False
    
    async def _try_click_elements(self):
        """尝试点击页面上的一些元素"""
        try:
            # 尝试点击搜索框、按钮等
            selectors = [
                "input[type='text']",
                "button",
                ".search-button",
                ".btn",
                "[role='button']"
            ]
            
            for selector in selectors:
                try:
                    elements = await self.page.querySelectorAll(selector)
                    if elements:
                        await elements[0].click()
                        await asyncio.sleep(1)
                        break
                except:
                    continue
                    
        except Exception as e:
            pass
    
    async def get_captured_secret(self) -> Optional[str]:
        """获取捕获到的secret"""
        try:
            secret = await self.page.evaluate('window.captured1688Secret')
            if secret:
                print(f"成功捕获 x-1688extension-secret: {secret[:50]}...")
                return secret
            return None
        except Exception as e:
            print(f"获取捕获的secret失败: {e}")
            return None
    
    async def get_captured_headers(self) -> list:
        """获取所有捕获到的headers"""
        try:
            headers = await self.page.evaluate('window.captured1688Headers')
            return headers or []
        except Exception as e:
            print(f"获取捕获的headers失败: {e}")
            return []
    
    async def extract_secret_from_extension(self) -> Optional[str]:
        """从扩展中直接提取secret生成逻辑"""
        try:
            if not self.extension_id:
                return None
            
            # 尝试访问扩展的background页面
            background_url = f"chrome-extension://{self.extension_id}/_generated_background_page.html"
            print(f"尝试访问扩展background页面: {background_url}")
            await self.page.goto(background_url)
            await asyncio.sleep(2)
            
            # 尝试执行扩展的secret生成函数
            script = """
            () => {
                try {
                    // 尝试调用可能的secret生成函数
                    if (typeof generateSecret === 'function') {
                        return generateSecret();
                    }
                    if (typeof window.generateSecret === 'function') {
                        return window.generateSecret();
                    }
                    // 查找可能的全局变量
                    for (let key in window) {
                        if (key.includes('secret') || key.includes('Secret')) {
                            return window[key];
                        }
                    }
                    return null;
                } catch (e) {
                    console.log('提取secret时出错:', e);
                    return null;
                }
            }
            """
            
            result = await self.page.evaluate(script)
            if result:
                print(f"从扩展中提取到secret: {result}")
                return result
                
        except Exception as e:
            print(f"从扩展提取secret失败: {e}")
        
        return None
    
    async def run_secret_extraction(self) -> Dict[str, Any]:
        """运行完整的secret提取流程"""
        result = {
            'success': False,
            'secret': None,
            'headers': [],
            'extension_id': None,
            'error': None
        }
        
        try:
            # 1. 启动Chrome并加载扩展
            print("正在启动Chrome浏览器...")
            await self.setup_chrome_with_extension()
            
            # 2. 尝试手动加载扩展（如果扩展路径存在）
            if self.extension_path and os.path.exists(self.extension_path) and os.path.isdir(self.extension_path):
                print("检测到扩展目录，尝试手动加载扩展...")
                await self.load_extension_manually()
            
            # 3. 获取扩展ID
            print("正在获取扩展ID...")
            extension_id = await self.get_extension_id()
            result['extension_id'] = extension_id
            
            # 4. 导航到1688
            await self.navigate_to_1688()
            
            # 5. 注入监控脚本
            print("正在注入网络监控脚本...")
            await self.inject_monitor_script()
            
            # 6. 触发扩展活动
            print("正在触发扩展活动...")
            await self.trigger_extension_activity()
            
            # 7. 获取结果
            secret = await self.get_captured_secret()
            headers = await self.get_captured_headers()
            
            if not secret and extension_id:
                # 8. 尝试从扩展直接提取
                print("尝试从扩展直接提取secret...")
                secret = await self.extract_secret_from_extension()
            
            result.update({
                'success': bool(secret),
                'secret': secret,
                'headers': headers
            })
            
            if secret:
                print(f"\n=== 成功获取 x-1688extension-secret ===")
                print(f"Secret: {secret}")
                print(f"长度: {len(secret)} 字符")
            else:
                print("\n=== 未能获取到 x-1688extension-secret ===")
                print("可能的原因:")
                print("1. 扩展未正确加载")
                print("2. 需要用户登录")
                print("3. 需要特定的触发条件")
                print("4. 扩展逻辑已更新")
                print("\n提示: 可以手动在浏览器中访问1688相关页面并观察网络请求")
            
        except Exception as e:
            result['error'] = str(e)
            print(f"提取过程中出错: {e}")
        
        return result
    
    async def close(self):
        """关闭浏览器并清理临时文件"""
        if self.browser:
            await self.browser.close()
        
        # 清理临时配置目录
        if self.profile_dir and os.path.exists(self.profile_dir):
            try:
                print(f"清理临时目录: {self.profile_dir}")
                shutil.rmtree(self.profile_dir)
            except Exception as e:
                print(f"清理临时目录失败: {e}")

async def main():
    """主函数"""
    print("=== 1688扩展 x-1688extension-secret 提取器 (Puppeteer版本) ===")
    print("注意: 需要安装 pyppeteer")
    print("pip install pyppeteer")
    print()
    
    # 获取扩展路径（可选）
    extension_path = input("请输入1688扩展的路径（直接回车跳过）: ").strip()
    if not extension_path:
        extension_path = None
    
    # 创建接口实例
    interface = Extension1688Interface(extension_path)
    
    try:
        # 运行提取流程
        result = await interface.run_secret_extraction()
        
        # 输出结果
        print("\n=== 提取结果 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result['success']:
            # 保存结果到文件
            with open('extracted_secret.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print("\n结果已保存到 extracted_secret.json")
        
        # 保持浏览器打开一段时间，方便手动操作
        print("\n浏览器将保持打开60秒，您可以手动操作...")
        await asyncio.sleep(60)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
    finally:
        await interface.close()

if __name__ == "__main__":
    asyncio.run(main())